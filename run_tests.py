#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبارات الأداة
"""

import os
import sys
import unittest
from unittest.mock import Mock, patch, MagicMock
import tempfile
import shutil

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from data_extractor import CertificateExtractor
from image_processor import ImageProcessor
from excel_manager import ExcelManager
from utils import validate_id, clean_text, generate_random_id

class TestDataExtractor(unittest.TestCase):
    def setUp(self):
        self.extractor = CertificateExtractor()
    
    def test_get_headers(self):
        """اختبار توليد headers"""
        headers = self.extractor.get_headers()
        self.assertIn('User-Agent', headers)
        self.assertIn('Accept', headers)
    
    @patch('requests.Session.get')
    def test_extract_certificate_data_success(self, mock_get):
        """اختبار استخراج البيانات بنجاح"""
        # محاكاة استجابة ناجحة
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = """
        <html>
            <body>
                <span id="studentName">أحمد محمد علي</span>
                <span id="college">كلية الطب</span>
                <span id="specialization">طب عام</span>
                <span id="year">2023</span>
                <span id="grade">امتياز</span>
                <span id="certificateNumber">123456</span>
            </body>
        </html>
        """
        mock_get.return_value = mock_response
        
        result = self.extractor.extract_certificate_data("6014820")
        
        self.assertIsNotNone(result)
        self.assertEqual(result['ID'], "6014820")
        self.assertIn('اسم الطالب', result)
    
    @patch('requests.Session.get')
    def test_extract_certificate_data_not_found(self, mock_get):
        """اختبار حالة عدم وجود الشهادة"""
        mock_response = Mock()
        mock_response.status_code = 404
        mock_get.return_value = mock_response
        
        result = self.extractor.extract_certificate_data("9999999")
        self.assertIsNone(result)

class TestImageProcessor(unittest.TestCase):
    def setUp(self):
        self.processor = ImageProcessor()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_get_file_extension(self):
        """اختبار تحديد امتداد الملف"""
        extension = self.processor._get_file_extension("/path/image.jpg", "")
        self.assertEqual(extension, ".jpg")
        
        extension = self.processor._get_file_extension("/path/image.png", "")
        self.assertEqual(extension, ".png")
    
    @patch('requests.Session.get')
    def test_download_certificate_image_no_url(self, mock_get):
        """اختبار تحميل صورة بدون رابط"""
        result = self.processor.download_certificate_image(None, "123456")
        self.assertIsNone(result)

class TestExcelManager(unittest.TestCase):
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.excel_file = os.path.join(self.temp_dir, "test.xlsx")
        self.manager = ExcelManager()
        self.manager.excel_file = self.excel_file
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_initialize_excel_file(self):
        """اختبار إنشاء ملف Excel"""
        self.manager.initialize_excel_file()
        self.assertTrue(os.path.exists(self.excel_file))
    
    def test_add_certificate_data(self):
        """اختبار إضافة بيانات شهادة"""
        test_data = {
            'ID': '123456',
            'اسم الطالب': 'أحمد محمد',
            'الكلية': 'كلية الطب',
            'التخصص': 'طب عام',
            'سنة التخرج': '2023',
            'التقدير': 'امتياز',
            'رقم الشهادة': '789',
            'تاريخ الاستخراج': '2024-01-01',
            'مسار الصورة': '',
            'حالة الاستخراج': 'نجح'
        }
        
        self.manager.add_certificate_data(test_data)
        self.assertTrue(os.path.exists(self.excel_file))
        
        # التحقق من وجود البيانات
        existing_ids = self.manager.get_existing_ids()
        self.assertIn('123456', existing_ids)

class TestUtils(unittest.TestCase):
    def test_validate_id(self):
        """اختبار التحقق من صحة ID"""
        self.assertTrue(validate_id("6014820"))
        self.assertTrue(validate_id(6014820))
        self.assertFalse(validate_id("abc"))
        self.assertFalse(validate_id("123"))  # قصير جداً
        self.assertFalse(validate_id("99999999"))  # طويل جداً
    
    def test_clean_text(self):
        """اختبار تنظيف النص"""
        self.assertEqual(clean_text("  نص مع مسافات  "), "نص مع مسافات")
        self.assertEqual(clean_text("نص\nمع\nأسطر"), "نص مع أسطر")
        self.assertEqual(clean_text(None), "")
    
    def test_generate_random_id(self):
        """اختبار توليد ID عشوائي"""
        random_id = generate_random_id(1000000, 9999999)
        self.assertTrue(1000000 <= random_id <= 9999999)

class TestIntegration(unittest.TestCase):
    """اختبارات التكامل"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @patch('config.OUTPUT_DIR', new_callable=lambda: tempfile.mkdtemp())
    @patch('config.IMAGES_DIR')
    @patch('config.EXCEL_FILE')
    def test_full_workflow_mock(self, mock_excel, mock_images, mock_output):
        """اختبار سير العمل الكامل مع محاكاة"""
        mock_output = self.temp_dir
        mock_images = os.path.join(self.temp_dir, "images")
        mock_excel = os.path.join(self.temp_dir, "test.xlsx")
        
        # إنشاء المجلدات
        os.makedirs(mock_images, exist_ok=True)
        
        # اختبار إنشاء Excel
        manager = ExcelManager()
        manager.excel_file = mock_excel
        manager.initialize_excel_file()
        
        self.assertTrue(os.path.exists(mock_excel))

def run_specific_test(test_class_name=None, test_method_name=None):
    """تشغيل اختبار محدد"""
    if test_class_name and test_method_name:
        suite = unittest.TestSuite()
        test_class = globals()[test_class_name]
        suite.addTest(test_class(test_method_name))
    elif test_class_name:
        suite = unittest.TestLoader().loadTestsFromTestCase(globals()[test_class_name])
    else:
        suite = unittest.TestLoader().discover('.', pattern='run_tests.py')
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

def main():
    """الدالة الرئيسية للاختبارات"""
    print("بدء تشغيل اختبارات الأداة...")
    print("=" * 50)
    
    # تشغيل جميع الاختبارات
    success = run_specific_test()
    
    if success:
        print("\n✅ جميع الاختبارات نجحت!")
    else:
        print("\n❌ بعض الاختبارات فشلت!")
        sys.exit(1)

if __name__ == "__main__":
    main()
