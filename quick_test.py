#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للأداة
"""

import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils import setup_directories, setup_logging
from data_extractor import CertificateExtractor
from image_processor import ImageProcessor
from excel_manager import ExcelManager

def test_single_certificate(certificate_id="6014820"):
    """اختبار استخراج شهادة واحدة"""
    print(f"🧪 اختبار استخراج الشهادة رقم: {certificate_id}")
    print("=" * 50)
    
    try:
        # إعداد البيئة
        setup_directories()
        logger = setup_logging()
        
        # إنشاء الكائنات
        extractor = CertificateExtractor()
        image_processor = ImageProcessor()
        excel_manager = ExcelManager()
        
        # استخراج البيانات
        print("📥 استخراج البيانات...")
        cert_data = extractor.extract_certificate_data(certificate_id)
        
        if cert_data:
            print("✅ تم استخراج البيانات بنجاح!")
            print("\n📋 البيانات المستخرجة:")
            for key, value in cert_data.items():
                if key != 'image_url':
                    print(f"   {key}: {value}")
            
            # تحميل الصورة
            image_path = None
            if 'image_url' in cert_data:
                print(f"\n🖼️  تحميل الصورة من: {cert_data['image_url']}")
                image_path = image_processor.download_certificate_image(
                    cert_data['image_url'], certificate_id
                )
                if image_path:
                    print(f"✅ تم حفظ الصورة في: {image_path}")
                    cert_data['مسار الصورة'] = image_path
                else:
                    print("❌ فشل في تحميل الصورة")
            
            # حفظ في Excel
            print("\n📊 حفظ البيانات في Excel...")
            excel_manager.add_certificate_data(cert_data, image_path)
            print("✅ تم حفظ البيانات في Excel بنجاح!")
            
            print(f"\n📁 ملف النتائج: {excel_manager.excel_file}")
            
        else:
            print("❌ لم يتم العثور على بيانات للشهادة")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()

def test_random_certificates(count=5):
    """اختبار استخراج شهادات عشوائية"""
    print(f"🎲 اختبار استخراج {count} شهادات عشوائية")
    print("=" * 50)
    
    try:
        from utils import generate_random_id
        from config import MIN_ID, MAX_ID
        
        setup_directories()
        logger = setup_logging()
        
        extractor = CertificateExtractor()
        excel_manager = ExcelManager()
        
        successful = 0
        failed = 0
        
        for i in range(count):
            random_id = generate_random_id(MIN_ID, MAX_ID)
            print(f"\n🔍 اختبار الرقم {i+1}/{count}: {random_id}")
            
            cert_data = extractor.extract_certificate_data(random_id)
            
            if cert_data:
                print(f"✅ نجح: {cert_data.get('اسم الطالب', 'غير محدد')}")
                excel_manager.add_certificate_data(cert_data)
                successful += 1
            else:
                print("❌ فشل: لا توجد بيانات")
                failed += 1
        
        print(f"\n📊 النتائج النهائية:")
        print(f"   ✅ نجح: {successful}")
        print(f"   ❌ فشل: {failed}")
        print(f"   📈 معدل النجاح: {(successful/count)*100:.1f}%")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")

def test_data_extraction_only(certificate_id="6014820"):
    """اختبار استخراج البيانات فقط بدون حفظ"""
    print(f"🔍 اختبار استخراج البيانات للرقم: {certificate_id}")
    print("=" * 50)
    
    try:
        setup_logging()
        extractor = CertificateExtractor()
        
        cert_data = extractor.extract_certificate_data(certificate_id)
        
        if cert_data:
            print("✅ تم استخراج البيانات:")
            for key, value in cert_data.items():
                print(f"   {key}: {value}")
        else:
            print("❌ لم يتم العثور على بيانات")
            
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")

def main():
    """القائمة الرئيسية للاختبارات"""
    print("🧪 أداة اختبار استخراج شهادات الأزهر")
    print("=" * 50)
    
    while True:
        print("\nاختر نوع الاختبار:")
        print("1. اختبار شهادة واحدة (6014820)")
        print("2. اختبار شهادة برقم مخصص")
        print("3. اختبار 5 شهادات عشوائية")
        print("4. اختبار استخراج البيانات فقط")
        print("5. خروج")
        
        choice = input("\nاختيارك (1-5): ").strip()
        
        if choice == "1":
            test_single_certificate()
        elif choice == "2":
            cert_id = input("أدخل رقم الشهادة: ").strip()
            if cert_id:
                test_single_certificate(cert_id)
        elif choice == "3":
            test_random_certificates()
        elif choice == "4":
            cert_id = input("أدخل رقم الشهادة (افتراضي 6014820): ").strip()
            if not cert_id:
                cert_id = "6014820"
            test_data_extraction_only(cert_id)
        elif choice == "5":
            print("👋 وداعاً!")
            break
        else:
            print("❌ اختيار غير صحيح")
        
        input("\nاضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()
