# أداة استخراج بيانات شهادات الأزهر

## الوصف
أداة متخصصة لاستخراج بيانات الشهادات من موقع الأزهر الشريف وحفظها في ملف Excel مع الصور المرفقة.

## المميزات
- استخراج البيانات تلقائياً من موقع الأزهر
- تحميل وحفظ صور الشهادات
- حفظ البيانات في ملف Excel منسق
- دعم الاستخراج العشوائي والمتسلسل
- واجهة سهلة الاستخدام
- نظام تسجيل شامل للعمليات
- إحصائيات مفصلة للنتائج

## المتطلبات
- Python 3.7 أو أحدث
- اتصال بالإنترنت
- مساحة تخزين كافية للصور

## التثبيت

1. تثبيت المتطلبات:
```bash
pip install -r requirements.txt
```

2. تشغيل الأداة:
```bash
python main.py
```

## طريقة الاستخدام

### 1. الاستخراج العشوائي
- يولد أرقام ID عشوائية ويختبرها
- مناسب للاستكشاف العام

### 2. الاستخراج المتسلسل
- يبدأ من رقم محدد ويستمر بالتسلسل
- مناسب للاستخراج المنظم

### 3. الاستخراج بنطاق محدد
- يستخرج البيانات من نطاق محدد من الأرقام
- مناسب للمراجعة المستهدفة

## هيكل الملفات

```
├── main.py              # الملف الرئيسي
├── config.py            # إعدادات الأداة
├── data_extractor.py    # وحدة استخراج البيانات
├── image_processor.py   # وحدة معالجة الصور
├── excel_manager.py     # وحدة إدارة Excel
├── utils.py             # وظائف مساعدة
├── requirements.txt     # المتطلبات
├── output/              # مجلد النتائج
│   ├── images/          # الصور المحملة
│   ├── certificates_data.xlsx  # ملف البيانات
│   └── extraction.log   # ملف السجل
└── README.md           # هذا الملف
```

## الإعدادات

يمكن تعديل الإعدادات في ملف `config.py`:

- `MIN_ID`, `MAX_ID`: نطاق أرقام الشهادات
- `DELAY_BETWEEN_REQUESTS`: التأخير بين الطلبات (ثواني)
- `TIMEOUT`: مهلة انتظار الاستجابة
- `MAX_RETRIES`: عدد محاولات إعادة الطلب

## البيانات المستخرجة

- رقم الشهادة
- اسم الطالب
- الكلية
- التخصص
- سنة التخرج
- التقدير
- رقم الشهادة الرسمي
- تاريخ الاستخراج
- مسار الصورة
- حالة الاستخراج

## الأمان والقانونية

⚠️ **تنبيه مهم**: هذه الأداة مخصصة للاستخدام الحكومي المصرح به فقط.

- تأكد من الحصول على التصاريح اللازمة
- احترم شروط استخدام الموقع
- استخدم التأخير المناسب بين الطلبات
- لا تستخدم الأداة لأغراض غير مشروعة

## استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في الاتصال**:
   - تحقق من الاتصال بالإنترنت
   - تأكد من عمل موقع الأزهر

2. **فشل في تحميل الصور**:
   - تحقق من مساحة التخزين
   - تأكد من صلاحيات الكتابة

3. **خطأ في Excel**:
   - تأكد من عدم فتح الملف في برنامج آخر
   - تحقق من صلاحيات الكتابة

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى مراجعة ملف السجل في `output/extraction.log`.

## الترخيص

هذه الأداة مخصصة للاستخدام الحكومي المصرح به فقط.
