# إعدادات متقدمة للأداة
import os
from datetime import datetime

class AdvancedConfig:
    """فئة الإعدادات المتقدمة"""
    
    def __init__(self):
        self.load_config()
    
    def load_config(self):
        """تحميل الإعدادات"""
        # إعدادات الشبكة المتقدمة
        self.PROXY_ENABLED = False
        self.PROXY_HOST = ""
        self.PROXY_PORT = ""
        self.PROXY_USERNAME = ""
        self.PROXY_PASSWORD = ""
        
        # إعدادات التحكم في السرعة
        self.ADAPTIVE_DELAY = True  # تأخير تكيفي حسب استجابة الخادم
        self.MIN_DELAY = 1  # أقل تأخير (ثواني)
        self.MAX_DELAY = 10  # أعلى تأخير (ثواني)
        self.BURST_MODE = False  # وضع الدفعات السريعة
        self.BURST_SIZE = 5  # حجم الدفعة
        self.BURST_DELAY = 30  # تأخير بين الدفعات
        
        # إعدادات إعادة المحاولة المتقدمة
        self.EXPONENTIAL_BACKOFF = True  # تأخير متزايد
        self.RETRY_ON_TIMEOUT = True
        self.RETRY_ON_CONNECTION_ERROR = True
        self.RETRY_ON_HTTP_ERROR = True
        
        # إعدادات التسجيل المتقدمة
        self.DETAILED_LOGGING = True
        self.LOG_REQUESTS = False  # تسجيل تفاصيل الطلبات
        self.LOG_RESPONSES = False  # تسجيل تفاصيل الاستجابات
        self.PERFORMANCE_LOGGING = True  # تسجيل الأداء
        
        # إعدادات الحفظ المتقدمة
        self.AUTO_BACKUP = True  # نسخ احتياطية تلقائية
        self.BACKUP_INTERVAL = 100  # كل كم شهادة
        self.COMPRESS_IMAGES = True  # ضغط الصور
        self.IMAGE_QUALITY = 85  # جودة الصور (1-100)
        self.THUMBNAIL_SIZE = (200, 200)  # حجم الصور المصغرة
        
        # إعدادات التحقق من البيانات
        self.VALIDATE_NAMES = True  # التحقق من صحة الأسماء
        self.VALIDATE_DATES = True  # التحقق من صحة التواريخ
        self.VALIDATE_IDS = True  # التحقق من صحة الأرقام
        self.CLEAN_DATA = True  # تنظيف البيانات تلقائياً
        
        # إعدادات الأمان
        self.RATE_LIMITING = True  # تحديد معدل الطلبات
        self.MAX_REQUESTS_PER_MINUTE = 30
        self.USER_AGENT_ROTATION = True  # تدوير User Agents
        self.RANDOM_HEADERS = True  # headers عشوائية
        
        # إعدادات التقارير
        self.GENERATE_REPORTS = True
        self.REPORT_INTERVAL = 50  # كل كم شهادة
        self.EMAIL_REPORTS = False  # إرسال تقارير بالبريد
        self.EMAIL_ADDRESS = ""
        
        # إعدادات قاعدة البيانات (اختيارية)
        self.USE_DATABASE = False
        self.DB_TYPE = "sqlite"  # sqlite, mysql, postgresql
        self.DB_HOST = "localhost"
        self.DB_PORT = 5432
        self.DB_NAME = "azhar_certificates"
        self.DB_USERNAME = ""
        self.DB_PASSWORD = ""
        
        # إعدادات التصدير
        self.EXPORT_FORMATS = ["xlsx", "csv", "json"]
        self.AUTO_EXPORT = False
        self.EXPORT_INTERVAL = 200
        
        # إعدادات الواجهة
        self.SHOW_PROGRESS_BAR = True
        self.SHOW_STATISTICS = True
        self.COLORED_OUTPUT = True
        self.ARABIC_INTERFACE = True
        
        # إعدادات الذاكرة والأداء
        self.MEMORY_LIMIT_MB = 1024  # حد الذاكرة بالميجابايت
        self.CACHE_SIZE = 100  # حجم التخزين المؤقت
        self.PARALLEL_DOWNLOADS = False  # تحميل متوازي للصور
        self.MAX_WORKERS = 3  # عدد العمليات المتوازية
        
        # إعدادات التوقف والاستئناف
        self.SAVE_STATE = True  # حفظ حالة التقدم
        self.STATE_FILE = "extraction_state.json"
        self.AUTO_RESUME = True  # استئناف تلقائي
        
        # إعدادات التنبيهات
        self.NOTIFICATIONS_ENABLED = False
        self.NOTIFICATION_INTERVAL = 100  # كل كم شهادة
        self.SOUND_NOTIFICATIONS = False
        
    def get_proxy_config(self):
        """الحصول على إعدادات البروكسي"""
        if not self.PROXY_ENABLED:
            return None
        
        proxy_config = {
            'http': f'http://{self.PROXY_HOST}:{self.PROXY_PORT}',
            'https': f'https://{self.PROXY_HOST}:{self.PROXY_PORT}'
        }
        
        if self.PROXY_USERNAME and self.PROXY_PASSWORD:
            auth = f'{self.PROXY_USERNAME}:{self.PROXY_PASSWORD}@'
            proxy_config['http'] = f'http://{auth}{self.PROXY_HOST}:{self.PROXY_PORT}'
            proxy_config['https'] = f'https://{auth}{self.PROXY_HOST}:{self.PROXY_PORT}'
        
        return proxy_config
    
    def get_delay(self, attempt=0, response_time=None):
        """حساب التأخير المناسب"""
        if not self.ADAPTIVE_DELAY:
            from config import DELAY_BETWEEN_REQUESTS
            return DELAY_BETWEEN_REQUESTS
        
        base_delay = self.MIN_DELAY
        
        # تأخير متزايد حسب المحاولة
        if self.EXPONENTIAL_BACKOFF and attempt > 0:
            base_delay = min(self.MIN_DELAY * (2 ** attempt), self.MAX_DELAY)
        
        # تعديل حسب وقت الاستجابة
        if response_time:
            if response_time > 5:  # استجابة بطيئة
                base_delay *= 1.5
            elif response_time < 1:  # استجابة سريعة
                base_delay *= 0.8
        
        return max(self.MIN_DELAY, min(base_delay, self.MAX_DELAY))
    
    def should_backup(self, processed_count):
        """تحديد ما إذا كان يجب عمل نسخة احتياطية"""
        return self.AUTO_BACKUP and processed_count % self.BACKUP_INTERVAL == 0
    
    def should_generate_report(self, processed_count):
        """تحديد ما إذا كان يجب إنشاء تقرير"""
        return self.GENERATE_REPORTS and processed_count % self.REPORT_INTERVAL == 0
    
    def get_backup_filename(self):
        """إنشاء اسم ملف النسخة الاحتياطية"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"backup_certificates_{timestamp}.xlsx"
    
    def validate_config(self):
        """التحقق من صحة الإعدادات"""
        errors = []
        
        if self.MIN_DELAY < 0:
            errors.append("MIN_DELAY يجب أن يكون أكبر من أو يساوي 0")
        
        if self.MAX_DELAY < self.MIN_DELAY:
            errors.append("MAX_DELAY يجب أن يكون أكبر من MIN_DELAY")
        
        if self.IMAGE_QUALITY < 1 or self.IMAGE_QUALITY > 100:
            errors.append("IMAGE_QUALITY يجب أن يكون بين 1 و 100")
        
        if self.MAX_REQUESTS_PER_MINUTE < 1:
            errors.append("MAX_REQUESTS_PER_MINUTE يجب أن يكون أكبر من 0")
        
        return errors
    
    def save_config(self, filename="user_config.json"):
        """حفظ الإعدادات في ملف"""
        import json
        
        config_dict = {
            key: value for key, value in self.__dict__.items()
            if not key.startswith('_')
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, ensure_ascii=False, indent=2)
    
    def load_config_from_file(self, filename="user_config.json"):
        """تحميل الإعدادات من ملف"""
        import json
        
        if os.path.exists(filename):
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    config_dict = json.load(f)
                
                for key, value in config_dict.items():
                    if hasattr(self, key):
                        setattr(self, key, value)
                        
            except Exception as e:
                print(f"خطأ في تحميل الإعدادات: {e}")

# إنشاء مثيل الإعدادات المتقدمة
advanced_config = AdvancedConfig()
