#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تثبيت وإعداد الأداة
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """التحقق من إصدار Python"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ خطأ: يتطلب Python 3.7 أو أحدث")
        print(f"الإصدار الحالي: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ إصدار Python مناسب: {version.major}.{version.minor}.{version.micro}")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    print("📁 إنشاء المجلدات...")
    
    directories = [
        "output",
        "output/images",
        "logs"
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ تم إنشاء المجلد: {directory}")
        except Exception as e:
            print(f"❌ خطأ في إنشاء المجلد {directory}: {e}")
            return False
    
    return True

def check_internet_connection():
    """التحقق من الاتصال بالإنترنت"""
    print("🌐 التحقق من الاتصال بالإنترنت...")
    
    try:
        import requests
        response = requests.get("https://www.google.com", timeout=5)
        if response.status_code == 200:
            print("✅ الاتصال بالإنترنت متاح")
            return True
    except:
        pass
    
    print("❌ تحذير: لا يوجد اتصال بالإنترنت أو ضعيف")
    return False

def test_installation():
    """اختبار التثبيت"""
    print("🧪 اختبار التثبيت...")
    
    try:
        # اختبار استيراد الوحدات الأساسية
        import requests
        import beautifulsoup4
        import openpyxl
        import PIL
        import pandas
        import tqdm
        
        print("✅ جميع الوحدات متاحة")
        
        # اختبار استيراد وحدات الأداة
        from utils import setup_directories, validate_id
        from config import BASE_URL, MIN_ID, MAX_ID
        
        print("✅ وحدات الأداة تعمل بشكل صحيح")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار التثبيت: {e}")
        return False

def create_desktop_shortcut():
    """إنشاء اختصار على سطح المكتب (Windows فقط)"""
    if platform.system() != "Windows":
        return
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "أداة استخراج شهادات الأزهر.lnk")
        target = os.path.join(os.getcwd(), "main.py")
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}"'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print("✅ تم إنشاء اختصار على سطح المكتب")
        
    except ImportError:
        print("ℹ️  لإنشاء اختصار سطح المكتب، قم بتثبيت: pip install winshell pywin32")
    except Exception as e:
        print(f"⚠️  لم يتم إنشاء اختصار سطح المكتب: {e}")

def print_usage_instructions():
    """طباعة تعليمات الاستخدام"""
    instructions = """
╔══════════════════════════════════════════════════════════════╗
║                        تم التثبيت بنجاح!                        ║
╚══════════════════════════════════════════════════════════════╝

📋 تعليمات الاستخدام:

1️⃣  لتشغيل الأداة:
   python main.py

2️⃣  لتشغيل الاختبارات:
   python run_tests.py

3️⃣  لقراءة التوثيق:
   اقرأ ملف README.md

4️⃣  ملفات النتائج ستكون في:
   📁 output/certificates_data.xlsx  (البيانات)
   📁 output/images/                 (الصور)
   📁 output/extraction.log          (السجل)

⚠️  تذكير مهم:
   - هذه الأداة للاستخدام الحكومي المصرح فقط
   - تأكد من الحصول على التصاريح اللازمة
   - احترم شروط استخدام الموقع

🆘 للدعم:
   راجع ملف السجل في output/extraction.log
"""
    print(instructions)

def main():
    """الدالة الرئيسية للتثبيت"""
    print("🚀 بدء تثبيت أداة استخراج شهادات الأزهر")
    print("=" * 60)
    
    # التحقق من إصدار Python
    if not check_python_version():
        sys.exit(1)
    
    # إنشاء المجلدات
    if not create_directories():
        print("❌ فشل في إنشاء المجلدات المطلوبة")
        sys.exit(1)
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("❌ فشل في تثبيت المتطلبات")
        sys.exit(1)
    
    # التحقق من الاتصال بالإنترنت
    check_internet_connection()
    
    # اختبار التثبيت
    if not test_installation():
        print("❌ فشل في اختبار التثبيت")
        sys.exit(1)
    
    # إنشاء اختصار سطح المكتب
    create_desktop_shortcut()
    
    # طباعة تعليمات الاستخدام
    print_usage_instructions()

if __name__ == "__main__":
    main()
